package com.ximalaya.galaxy.business.business.boss

import com.alibaba.nacos.api.config.annotation.NacosValue
import com.ximalaya.galaxy.business.common.support.SplitHelper
import org.springframework.beans.factory.annotation.Value
import org.springframework.cloud.context.config.annotation.RefreshScope
import org.springframework.stereotype.Component

/**
 *<AUTHOR>
 *@create 2025-05-06 17:24
 */
@RefreshScope
@Component
class BossNacosConfigBean {

    @Value(value = "\${hello.world}")
    var helloFootball: String? = null

    @Value(value = "galaxy.admins")
    var galaxyAdmins: String? = null

    fun getAdmins(): List<Long> {
        return SplitHelper.deserialize(galaxyAdmins) { java.lang.Long.valueOf(it) }
    }

    fun isAdmin(uid: Long): Boolean {
        return getAdmins().contains(uid)
    }

    @Value(value = "user.inner")
    var innerUsers: String? = null

    fun getInnerUsers(): List<Long> {
        return SplitHelper.deserialize(innerUsers) { java.lang.Long.valueOf(it) }
    }

    fun isInnerUser(uid: Long): Bo<PERSON>an {
        return getInnerUsers().contains(uid)
    }

}