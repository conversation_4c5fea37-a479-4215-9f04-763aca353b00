package com.ximalaya.galaxy.business.common.message

import java.io.Serializable
import java.time.LocalDateTime

/**
 * Galaxy消息实体类
 */
data class GalaxyMessage(
    val id: String,
    val type: String,
    val content: String,
    val sender: String,
    val receiver: String? = null,
    val timestamp: LocalDateTime = LocalDateTime.now(),
    val metadata: Map<String, Any> = emptyMap()
) : Serializable {
    companion object {
        const val TOPIC = "galaxy-message-topic"
        const val PRODUCER_GROUP = "galaxy-business-worker-producer-group"
        const val CONSUMER_GROUP = "galaxy-business-boss-consumer-group"
    }
} 