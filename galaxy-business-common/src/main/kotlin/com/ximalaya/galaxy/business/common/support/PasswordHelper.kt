package com.ximalaya.galaxy.business.common.support

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.stereotype.Component

/**
 * 密码加密工具类
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
@Component
class PasswordHelper {
    
    private val passwordEncoder = BCryptPasswordEncoder()
    
    /**
     * 加密密码
     */
    fun encode(password: String): String {
        return passwordEncoder.encode(password)
    }
    
    /**
     * 验证密码
     */
    fun matches(rawPassword: String, encodedPassword: String): Bo<PERSON>an {
        return passwordEncoder.matches(rawPassword, encodedPassword)
    }
} 