package com.ximalaya.galaxy.business.repo.service.impl

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.ximalaya.galaxy.business.repo.entity.UserLoginLogEntity
import com.ximalaya.galaxy.business.repo.mapper.UserLoginLogMapper
import com.ximalaya.galaxy.business.repo.service.UserLoginLogService
import org.springframework.stereotype.Service

/**
 * 用户登录日志Service实现类
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
@Service
class UserLoginLogServiceImpl : ServiceImpl<UserLoginLogMapper, UserLoginLogEntity>(), UserLoginLogService {
    
    override fun save(logEntity: UserLoginLogEntity): Boolean {
        return this.save(logEntity)
    }
} 