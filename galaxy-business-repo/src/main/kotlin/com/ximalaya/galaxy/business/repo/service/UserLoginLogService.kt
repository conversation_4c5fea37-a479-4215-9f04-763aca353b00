package com.ximalaya.galaxy.business.repo.service

import com.baomidou.mybatisplus.extension.service.IService
import com.ximalaya.galaxy.business.repo.entity.UserLoginLogEntity

/**
 * 用户登录日志Service接口
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
interface UserLoginLogService : IService<UserLoginLogEntity> {
    
    /**
     * 保存登录日志
     */
    override fun save(logEntity: UserLoginLogEntity): Boolean
} 