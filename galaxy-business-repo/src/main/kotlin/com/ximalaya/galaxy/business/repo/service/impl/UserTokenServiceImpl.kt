package com.ximalaya.galaxy.business.repo.service.impl

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.ximalaya.galaxy.business.repo.entity.UserTokenEntity
import com.ximalaya.galaxy.business.repo.mapper.UserTokenMapper
import com.ximalaya.galaxy.business.repo.service.UserTokenService
import org.springframework.stereotype.Service
import java.time.LocalDateTime

/**
 * 用户Token Service实现类
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
@Service
class UserTokenServiceImpl : ServiceImpl<UserTokenMapper, UserTokenEntity>(), UserTokenService {
    
    override fun findByToken(token: String): UserTokenEntity? {
        return this.ktQuery()
            .eq(UserTokenEntity::token, token)
            .eq(UserTokenEntity::status, 1)
            .one()
    }
    
    override fun findValidByUserId(userId: Long): UserTokenEntity? {
        return this.ktQuery()
            .eq(UserTokenEntity::userId, userId)
            .eq(UserTokenEntity::status, 1)
            .gt(UserTokenEntity::expireTime, LocalDateTime.now())
            .one()
    }
    
    override fun save(token: UserTokenEntity): Boolean {
        return this.save(token)
    }
    
    override fun invalidateToken(token: String): Boolean {
        return this.ktUpdate()
            .eq(UserTokenEntity::token, token)
            .set(UserTokenEntity::status, 0)
            .update()
    }
    
    override fun invalidateAllUserTokens(userId: Long): Boolean {
        return this.ktUpdate()
            .eq(UserTokenEntity::userId, userId)
            .set(UserTokenEntity::status, 0)
            .update()
    }
    
    override fun cleanExpiredTokens(): Boolean {
        return this.ktUpdate()
            .lt(UserTokenEntity::expireTime, LocalDateTime.now())
            .set(UserTokenEntity::status, 0)
            .update()
    }
} 