package com.ximalaya.galaxy.business.repo.service

import com.baomidou.mybatisplus.extension.service.IService
import com.ximalaya.galaxy.business.repo.entity.UserTokenEntity

/**
 * 用户Token Service接口
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
interface  UserTokenService : IService<UserTokenEntity> {
    
    /**
     * 根据token查找
     */
    fun findByToken(token: String): UserTokenEntity?
    
    /**
     * 根据用户ID查找有效token
     */
    fun findValidByUserId(userId: Long): UserTokenEntity?
    
    /**
     * 保存token
     */
    override fun save(token: UserTokenEntity): Boolean
    
    /**
     * 使token失效
     */
    fun invalidateToken(token: String): Bo<PERSON><PERSON>
    
    /**
     * 使用户所有token失效
     */
    fun invalidateAllUserTokens(userId: Long): Boolean
    
    /**
     * 清理过期token
     */
    fun cleanExpiredTokens(): <PERSON><PERSON><PERSON>
} 