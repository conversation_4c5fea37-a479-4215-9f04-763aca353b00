package com.ximalaya.galaxy.business.repo.service.impl

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.ximalaya.galaxy.business.repo.entity.UserEntity
import com.ximalaya.galaxy.business.repo.mapper.UserMapper
import com.ximalaya.galaxy.business.repo.service.UserService
import org.springframework.stereotype.Service
import java.time.LocalDateTime

/**
 * 用户Service实现类
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
@Service
class UserServiceImpl : ServiceImpl<UserMapper, UserEntity>(), UserService {
    
    override fun findByUsername(username: String): UserEntity? {
        return this.ktQuery()
            .eq(UserEntity::username, username)
            .one()
    }
    
    override fun findById(id: Long): UserEntity? {
        return this.getById(id)
    }
    
    override fun save(user: UserEntity): <PERSON><PERSON><PERSON> {
        return this.save(user)
    }
    
    override fun update(user: UserEntity): Boolean {
        return this.updateById(user)
    }
    
    override fun updateLastLoginInfo(id: Long, loginTime: LocalDateTime, loginIp: String?): Boolean {
        return this.ktUpdate()
            .eq(UserEntity::id, id)
            .set(UserEntity::lastLoginTime, loginTime)
            .set(UserEntity::lastLoginIp, loginIp)
            .update()
    }
    
    override fun existsByUsername(username: String): Boolean {
        return this.ktQuery()
            .eq(UserEntity::username, username)
            .exists()
    }
    

} 