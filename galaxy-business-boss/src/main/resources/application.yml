server:
  port: @server.port@
  servlet:
    context-path: /${spring.application.name}
    encoding:
      enabled: true
      force: true
      charset: utf-8

spring:
  application:
    name: @app.name@
  profiles:
    active: @active.profile@
  mvc:
    static-path-pattern: /resources/**
  shardingsphere:
    datasource:
      names: galaxy-ds
      galaxy-ds:
        url: @jdbc.url.0@
        username: @jdbc.username.0@
        password: @jdbc.password.0@
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
    sharding:
      tables:
        gxy_session:
          actual-data-nodes: galaxy-ds.gxy_session_$->{0..9}
          table-strategy:
            standard:
              sharding-column: uid
              precise-algorithm-class-name: com.ximalaya.galaxy.business.repo.algorithm.GalaxySessionShardingAlgorithm
          key-generator:
            type: SNOWFLAKE
            column: session_code
        gxy_phase:
          actual-data-nodes: galaxy-ds.gxy_phase_$->{0..19}
          table-strategy:
            standard:
              sharding-column: session_code
              precise-algorithm-class-name: com.ximalaya.galaxy.business.repo.algorithm.GalaxyPhaseShardingAlgorithm
          key-generator:
            type: SN<PERSON><PERSON>AKE
            column: phase_code
        gxy_block:
          actual-data-nodes: galaxy-ds.gxy_block_$->{0..19}
          table-strategy:
            standard:
              sharding-column: session_code
              precise-algorithm-class-name: com.ximalaya.galaxy.business.repo.algorithm.GalaxyBlockShardingAlgorithm
          key-generator:
            type: SNOWFLAKE
            column: block_code
  # freemarker模板引擎
  freemarker:
    template-loader-path:
      - classpath:/templates/
    cache: false
    charset: UTF-8
    content-type: text/html
    suffix: .ftlh
    allow-request-override: true
    allow-session-override: true
    expose-request-attributes: true
    expose-spring-macro-helpers: true
  # redis
  redis:
    database: @redis.database@
    host: @redis.host@
    port: @redis.port@
    password: @redis.password@
    # 链接超时时间 单位 ms（毫秒）
    timeout: 10000ms
    lettuce:
      pool:
        # 连接池最大连接数（使用负值表示没有限制） 默认 8
        max-active: 8
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
        max-wait: -1
        # 连接池中的最大空闲连接 默认 8
        max-idle: 8
        # 连接池中的最小空闲连接 默认 0
        min-idle: 0

# RocketMQ配置
rocketmq:
  name-server: ************:9876
  consumer:
    group: galaxy-business-boss-consumer-group
    consume-timeout: 15
    retry-times-when-send-failed: 2

login-auth:
  type: interceptor  # 自动配置的类型, 可选值 filter和interceptor
  app-name: @app.name@

# mybatis-plus
mybatis-plus:
  globalConfig:
    dbConfig:
      idType: AUTO
      tablePrefix: gxy_
  mapper-locations: classpath:mapper/*.xml

galaxy:
  home-page-path: http://static2.test.ximalaya.com/yx/galaxy-web/last/dist/index.html?v=
  security-key: 9680e7dfdd8d933554ac3e2067bd2210c1c4cc28dd6b0fb7bf8d1e01a49dfdb4
  agent:
    path: https://sse.test.ximalaya.com/galaxy-host
  dify:
    path: http://ops.pd.ximalaya.local/dify/v1/workflows/run
    app-secret:
      regenerate-parent-title: app-ZGhXSvNNWxAI4xPc6GfssuAq
      regenerate-track-name: app-tzqITOOxm8yXw7SEyGAnbOI4

# JWT配置
jwt:
  secret: galaxy-business-secret-key-2025
  expiration: 86400  # 24小时，单位秒
  refresh-expiration: 604800  # 7天，单位秒

# 日志配置
logging:
  file:
    name: @app.log@
  pattern:
    file: "${FILE_LOG_PATTERN:-%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39}  %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"
    console: "%clr(%d){faint} %clr(%p) %clr(${PID:- }){magenta} %clr([%15.15t]){faint} %C %clr([%F:%L]){cyan} %m%n%wEx"
  level:
    root: info
    dev.legionai.legion: @app.log.level@