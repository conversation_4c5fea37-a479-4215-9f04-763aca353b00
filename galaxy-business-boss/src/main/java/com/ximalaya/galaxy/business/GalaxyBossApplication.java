package com.ximalaya.galaxy.business;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @create 2023-02-15 16:02
 */
@EnableScheduling
public class GalaxyBossApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(GalaxyBossApplication.class);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(GalaxyBossApplication.class);
    }

}
